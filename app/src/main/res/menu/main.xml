<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <item
        android:id="@+id/action_home"
        android:icon="@drawable/ic_menu_home"
        android:orderInCategory="80"
        android:title="@string/menu_home"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/action_search"
        android:icon="@drawable/ic_search"
        android:orderInCategory="90"
        android:title="@string/search"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/action_logout"
        android:icon="@drawable/ic_logout"
        android:orderInCategory="100"
        android:title="@string/logout"
        app:showAsAction="ifRoom" />
</menu>
