<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:showIn="navigation_view">

    <group android:checkableBehavior="single">
        <item
            android:id="@+id/nav_home"
            android:icon="@drawable/ic_menu_home"
            android:title="@string/menu_home" />
        <item
            android:id="@+id/nav_tickets"
            android:icon="@drawable/ic_menu_tickets"
            android:title="@string/menu_tickets" />
        <item
            android:id="@+id/nav_new_ticket"
            android:icon="@drawable/ic_menu_new"
            android:title="@string/new_ticket" />
        <item
            android:id="@+id/nav_knowledges"
            android:icon="@drawable/ic_menu_tickets"
            android:title="@string/menu_knowledges" />
        <item
            android:id="@+id/nav_new_knowledge"
            android:icon="@drawable/ic_menu_new"
            android:title="@string/new_knowledge" />
        <item
            android:id="@+id/nav_faqs"
            android:icon="@drawable/ic_menu_tickets"
            android:title="@string/menu_faqs" />
        <item
            android:id="@+id/nav_new_faq"
            android:icon="@drawable/ic_menu_new"
            android:title="@string/new_faq" />
        <item
            android:id="@+id/nav_events"
            android:icon="@drawable/ic_menu_tickets"
            android:title="@string/menu_events" />
        <item
            android:id="@+id/nav_new_event"
            android:icon="@drawable/ic_menu_new"
            android:title="@string/new_event" />
        <item
            android:id="@+id/nav_profile"
            android:icon="@drawable/ic_menu_profile"
            android:title="@string/menu_profile" />
        <item
            android:id="@+id/nav_users"
            android:icon="@drawable/ic_menu_users"
            android:title="@string/menu_users" />
    </group>
</menu>
