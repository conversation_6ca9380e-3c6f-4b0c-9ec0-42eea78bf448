package com.ariel.app.ui.events

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast

import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ariel.app.R
import com.ariel.app.data.SessionManager
import com.ariel.app.databinding.FragmentEventsBinding

/**
 * Fragment for displaying a list of event items.
 */
class EventsFragment : Fragment() {

    private var _binding: FragmentEventsBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: EventsViewModel
    private lateinit var eventAdapter: EventAdapter
    private lateinit var sessionManager: SessionManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(EventsViewModel::class.java)
        _binding = FragmentEventsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())
        setupRecyclerView()
        setupNewEventButton()
        observeViewModel()
        fetchEvents()
    }

    /**
     * Sets up the new event button.
     * Only shows the button for superusers.
     */
    private fun setupNewEventButton() {
        val user = sessionManager.getUser()
        if (user != null && user.isSuperuser) {
            // Only superusers can create events
            binding.fabNewEvent.setOnClickListener {
                try {
                    // Show a toast to indicate we're trying to navigate
                    Toast.makeText(requireContext(), "Navigating to New Event page...", Toast.LENGTH_SHORT).show()

                    // Navigate to the new event page
                    findNavController().navigate(R.id.action_nav_events_to_nav_new_event)
                } catch (e: Exception) {
                    // If navigation fails, show the error
                    Toast.makeText(requireContext(), "Navigation error: ${e.message}", Toast.LENGTH_LONG).show()
                    Log.e("EventsFragment", "Navigation error", e)
                }
            }
            binding.fabNewEvent.visibility = View.VISIBLE
        } else {
            // Hide the button for regular users
            binding.fabNewEvent.visibility = View.GONE
        }
    }



    /**
     * Sets up the RecyclerView for displaying event items with pagination.
     */
    private fun setupRecyclerView() {
        eventAdapter = EventAdapter { event ->
            // Navigate to event details
            val bundle = Bundle().apply {
                putString("shortUuid", event.shortUuid)
            }
            findNavController().navigate(R.id.action_nav_events_to_eventDetailsFragment, bundle)
        }

        val layoutManager = LinearLayoutManager(requireContext())
        binding.recyclerEvents.apply {
            this.layoutManager = layoutManager
            adapter = eventAdapter

            // Add scroll listener for pagination
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    val visibleItemCount = layoutManager.childCount
                    val totalItemCount = layoutManager.itemCount
                    val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()

                    // Check if we're near the bottom and should load more
                    val isLoadingMore = viewModel.isLoadingMore.value ?: false
                    if (!isLoadingMore && viewModel.hasMorePages()) {
                        if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 3) {
                            // Load more when 3 items from the bottom
                            loadMoreEvents()
                        }
                    }
                }
            })
        }
    }

    /**
     * Observes changes in the ViewModel's LiveData.
     */
    private fun observeViewModel() {
        viewModel.events.observe(viewLifecycleOwner) { events ->
            eventAdapter.submitList(events)

            // Show empty view if the list is empty
            if (events.isEmpty()) {
                binding.tvEmpty.visibility = View.VISIBLE
                binding.recyclerEvents.visibility = View.GONE
            } else {
                binding.tvEmpty.visibility = View.GONE
                binding.recyclerEvents.visibility = View.VISIBLE
            }
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE

            if (isLoading) {
                binding.tvError.visibility = View.GONE
                binding.tvEmpty.visibility = View.GONE
            }
        }

        viewModel.isLoadingMore.observe(viewLifecycleOwner) { isLoadingMore ->
            binding.progressBarPagination.visibility = if (isLoadingMore) View.VISIBLE else View.GONE
        }

        viewModel.error.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                binding.tvError.text = errorMessage
                binding.tvError.visibility = View.VISIBLE
                binding.recyclerEvents.visibility = View.GONE
                binding.tvEmpty.visibility = View.GONE
            } else {
                binding.tvError.visibility = View.GONE
            }
        }
    }

    /**
     * Fetches event items from the API.
     */
    private fun fetchEvents() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.fetchEvents(token)
        } else {
            binding.tvError.text = getString(R.string.authentication_token_not_found)
            binding.tvError.visibility = View.VISIBLE
            binding.recyclerEvents.visibility = View.GONE
        }
    }

    /**
     * Loads more event items for pagination.
     */
    private fun loadMoreEvents() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.loadMoreEvents(token)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
