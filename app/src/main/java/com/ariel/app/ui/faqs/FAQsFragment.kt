package com.ariel.app.ui.faqs

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup

import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ariel.app.R
import com.ariel.app.data.SessionManager
import com.ariel.app.databinding.FragmentFaqsBinding

/**
 * Fragment for displaying a list of FAQ items.
 */
class FAQsFragment : Fragment() {

    private var _binding: FragmentFaqsBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: FAQsViewModel
    private lateinit var faqAdapter: FAQAdapter
    private lateinit var sessionManager: SessionManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(FAQsViewModel::class.java)
        _binding = FragmentFaqsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())
        setupRecyclerView()
        setupNewFAQButton()
        observeViewModel()
        fetchFAQs()
    }

    /**
     * Sets up the new FAQ button.
     * Only shows the button for superusers.
     */
    private fun setupNewFAQButton() {
        val user = sessionManager.getUser()
        if (user != null && user.isSuperuser) {
            // Only superusers can create FAQ items
            binding.fabNewFaq.setOnClickListener {
                findNavController().navigate(R.id.action_nav_faqs_to_nav_new_faq)
            }
            binding.fabNewFaq.visibility = View.VISIBLE
        } else {
            // Hide the button for regular users
            binding.fabNewFaq.visibility = View.GONE
        }
    }



    /**
     * Sets up the RecyclerView for displaying FAQ items with pagination.
     */
    private fun setupRecyclerView() {
        faqAdapter = FAQAdapter { faq ->
            // Navigate to FAQ details
            val bundle = Bundle().apply {
                putString("shortUuid", faq.shortUuid)
            }
            findNavController().navigate(R.id.action_nav_faqs_to_faqDetailsFragment, bundle)
        }

        val layoutManager = LinearLayoutManager(requireContext())
        binding.recyclerFaqs.apply {
            this.layoutManager = layoutManager
            adapter = faqAdapter

            // Add scroll listener for pagination
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    val visibleItemCount = layoutManager.childCount
                    val totalItemCount = layoutManager.itemCount
                    val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()

                    // Check if we're near the bottom and should load more
                    val isLoadingMore = viewModel.isLoadingMore.value ?: false
                    if (!isLoadingMore && viewModel.hasMorePages()) {
                        if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 3) {
                            // Load more when 3 items from the bottom
                            loadMoreFAQs()
                        }
                    }
                }
            })
        }
    }

    /**
     * Observes changes in the ViewModel's LiveData.
     */
    private fun observeViewModel() {
        viewModel.faqs.observe(viewLifecycleOwner) { faqs ->
            faqAdapter.submitList(faqs)

            // Show empty view if the list is empty
            if (faqs.isEmpty()) {
                binding.tvEmpty.visibility = View.VISIBLE
                binding.recyclerFaqs.visibility = View.GONE
            } else {
                binding.tvEmpty.visibility = View.GONE
                binding.recyclerFaqs.visibility = View.VISIBLE
            }
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE

            if (isLoading) {
                binding.tvError.visibility = View.GONE
                binding.tvEmpty.visibility = View.GONE
            }
        }

        viewModel.isLoadingMore.observe(viewLifecycleOwner) { isLoadingMore ->
            binding.progressBarPagination.visibility = if (isLoadingMore) View.VISIBLE else View.GONE
        }

        viewModel.error.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                binding.tvError.text = errorMessage
                binding.tvError.visibility = View.VISIBLE
                binding.recyclerFaqs.visibility = View.GONE
                binding.tvEmpty.visibility = View.GONE
            } else {
                binding.tvError.visibility = View.GONE
            }
        }
    }

    /**
     * Fetches FAQ items from the API.
     */
    private fun fetchFAQs() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.fetchFAQs(token)
        } else {
            binding.tvError.text = getString(R.string.authentication_token_not_found)
            binding.tvError.visibility = View.VISIBLE
            binding.recyclerFaqs.visibility = View.GONE
        }
    }

    /**
     * Loads more FAQ items for pagination.
     */
    private fun loadMoreFAQs() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.loadMoreFAQs(token)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
